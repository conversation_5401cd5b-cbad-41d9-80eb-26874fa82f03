/**
 * <PERSON><PERSON><PERSON> to inspect password format in the database
 * This helps us understand how Better Auth stores passwords
 */
import { neon } from '@neondatabase/serverless';
import 'dotenv/config';

// Create database connection
const sql = neon(process.env.DATABASE_URL);

async function inspectPasswordFormat() {
	try {
		// Find accounts with passwords (credential provider)
		const accounts = await sql`
			SELECT id, "accountId", "providerId", password
			FROM account
			WHERE "providerId" = 'credential'
			AND password IS NOT NULL
			LIMIT 5
		`;

		console.log('Found accounts with passwords:');
		accounts.forEach((acc, index) => {
			console.log(`\nAccount ${index + 1}:`);
			console.log(`  ID: ${acc.id}`);
			console.log(`  Email: ${acc.accountId}`);
			console.log(`  Password format: ${acc.password ? 'Present' : 'Missing'}`);
			if (acc.password) {
				console.log(`  Password length: ${acc.password.length}`);
				console.log(`  Password starts with: ${acc.password.substring(0, 20)}...`);
				console.log(`  Contains colon: ${acc.password.includes(':')}`);
				console.log(`  Parts when split by colon: ${acc.password.split(':').length}`);
				if (acc.password.includes(':')) {
					const parts = acc.password.split(':');
					console.log(`  First part length: ${parts[0].length}`);
					console.log(`  Second part length: ${parts[1].length}`);
				}
			}
		});

		if (accounts.length === 0) {
			console.log('No accounts with passwords found. Creating a test user via Better Auth API...');

			// Try to create a user via Better Auth to see the format
			const testEmail = `format-test-${Date.now()}@example.com`;
			const testPassword = 'TestPass123!';

			console.log(`Creating test user: ${testEmail}`);

			const response = await fetch('http://localhost:3000/api/auth/sign-up/email', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					name: 'Format Test User',
					email: testEmail,
					password: testPassword,
				}),
			});

			if (response.ok) {
				console.log('Test user created successfully. Checking password format...');

				// Query the newly created account
				const newAccount = await db.select().from(account).where(eq(account.accountId, testEmail)).limit(1);

				if (newAccount.length > 0) {
					const acc = newAccount[0];
					console.log('\nNewly created account password format:');
					console.log(`  Password length: ${acc.password?.length}`);
					console.log(`  Password: ${acc.password?.substring(0, 50)}...`);
					console.log(`  Contains colon: ${acc.password?.includes(':')}`);
					if (acc.password?.includes(':')) {
						const parts = acc.password.split(':');
						console.log(`  Parts: ${parts.length}`);
						console.log(`  First part length: ${parts[0].length}`);
						console.log(`  Second part length: ${parts[1].length}`);
					}
				}
			} else {
				console.log('Failed to create test user:', await response.text());
			}
		}
	} catch (error) {
		console.error('Error inspecting password format:', error);
	}
}

inspectPasswordFormat();
