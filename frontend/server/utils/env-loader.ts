import { config } from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

export type Environment = 'development' | 'test' | 'production';

/**
 * Loads environment variables for the specified environment
 * @param environment The environment to load variables for
 * @returns The loaded environment variables
 */
export function loadEnv(environment: Environment = 'test') {
	if (environment === 'test') {
		// Load base test env vars
		config({ path: '.env.test' });

		// Load local overrides if they exist
		const localEnvPath = path.resolve(process.cwd(), '.env.test.local');
		if (fs.existsSync(localEnvPath)) {
			config({ path: localEnvPath, override: true });
			console.log('Loaded test environment variables from .env.test.local');
		} else {
			console.warn('No .env.test.local file found. Make sure to create it with your Neon test branch connection string.');
		}
	} else if (environment === 'development') {
		// For development, use .env.local
		const localEnvPath = path.resolve(process.cwd(), '.env.local');
		if (fs.existsSync(localEnvPath)) {
			config({ path: localEnvPath });
			console.log('Loaded development environment variables from .env.local');
		} else {
			console.warn('No .env.local file found. Make sure to create it with your development configuration.');
		}
	} else if (environment === 'production') {
		// For production, DATABASE_URL should be available as environment variable
		// No need to load from .env files in production
		console.log('Production environment: using environment variables');
	}

	return process.env;
}

/**
 * Loads environment variables for testing (backward compatibility)
 * @returns The loaded environment variables
 */
export function loadTestEnv() {
	return loadEnv('test');
}

/**
 * Converts process.env to a Record<string, string> for Playwright
 */
export function getEnvRecord(): Record<string, string> {
	return Object.entries(process.env).reduce((acc, [key, value]) => {
		if (value !== undefined) {
			acc[key] = value;
		}
		return acc;
	}, {} as Record<string, string>);
}
