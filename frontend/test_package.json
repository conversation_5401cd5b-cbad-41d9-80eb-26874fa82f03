// tests/e2e/utils/db-helpers.ts
import { drizzle } from 'drizzle-orm/neon-serverless';
import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import * as schema from '../../../server/db/schema';
import { eq } from 'drizzle-orm';

// Load test environment variables
dotenv.config({ path: '.env.test' });

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema });

/**
 * Resets test database by removing all test users
 */
export async function resetTestDatabase() {
	// Delete test users that may have been created in previous test runs
	await db.delete(schema.users).where(
		eq(schema.users.email, '<EMAIL>')
	);
	
	console.log('Test database reset completed');
}

/**
 * Creates a test user if it doesn't exist
 */
export async function createTestUser(email = '<EMAIL>', password = 'Password123!') {
	// Implementation would depend on your authentication system
	// This is a placeholder - you'll need to adapt it to your actual auth implementation
	
	console.log(`Created test user: ${email}`);
	return { email, password };
}

/**
 * Main setup function to prepare database for tests
 */
export async function setupTestDatabase() {
	await resetTestDatabase();
	console.log('Test database setup completed');
}