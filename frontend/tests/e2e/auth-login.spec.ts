import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad, signInUser } from './helpers.js';
import { deleteTestUser, generateTestUserData } from './test-users.js';

// Test user for sign-in tests - we'll create this via the UI with unique email
const SIGNIN_TEST_USER = {
	name: 'Sign In Test User',
	email: `signin-test-${Date.now()}@example.com`,
	password: 'SignInTest123!',
};

test.describe('Login Page Authentication', () => {
	// Create our test user via sign-up form before running sign-in tests
	test.beforeAll(async ({ browser }) => {
		// First, clean up any existing test user with incorrect password hash
		await deleteTestUser(SIGNIN_TEST_USER.email);

		const context = await browser.newContext();
		const page = await context.newPage();

		try {
			// Create the test user via sign-up form (uses Better Auth's proper password hashing)
			await page.goto('/sign-up');
			await waitForPageLoad(page);

			await page.getByPlaceholder('Name').fill(SIGNIN_TEST_USER.name);
			await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
			await page.getByPlaceholder('Password').fill(SIGNIN_TEST_USER.password);

			await page.getByRole('button', { name: 'Sign Up with Email' }).click();

			// Wait a bit for the sign-up to complete
			await page.waitForTimeout(2000);
		} catch (error) {
			// If sign-up fails, log the error
			console.log('Test user creation failed:', error);
			throw error; // Re-throw to fail the test setup if user creation fails
		} finally {
			await context.close();
		}
	});

	test('allows access to login page when signed out', async ({ page, goto }) => {
		test.slow();

		// Navigate to sign-in page
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Should see the sign-in form
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
		await expect(page.getByPlaceholder('Email address')).toBeVisible();
		await expect(page.getByPlaceholder('Password')).toBeVisible();
		await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();
	});

	test('redirects away from login page when already signed in', async ({ page, goto }) => {
		test.slow();

		// First sign in
		await signInUser(page, SIGNIN_TEST_USER.email, SIGNIN_TEST_USER.password);

		// Try to navigate to sign-in page
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Should be redirected away from sign-in page (middleware should redirect to /)
		await expect(page).not.toHaveURL('/sign-in');
		await expect(page).toHaveURL('/');
	});

	test('successfully signs up a new user with email/password', async ({ page, goto }) => {
		test.slow();

		// Generate unique test user data
		const testUserData = generateTestUserData({
			name: 'New Test User',
		});

		try {
			// Navigate to sign-up page
			await goto('/sign-up');
			await waitForPageLoad(page);

			// Should see the sign-up form
			await expect(page.getByText('Sign up to Foundation')).toBeVisible();

			// Fill in the sign-up form
			await page.getByPlaceholder('Name').fill(testUserData.name);
			await page.getByPlaceholder('Email address').fill(testUserData.email);
			await page.getByPlaceholder('Password').fill(testUserData.password);

			// Submit the form
			await page.getByRole('button', { name: 'Sign Up with Email' }).click();

			// Should navigate away from sign-up page on success
			await page.waitForURL((url) => !url.pathname.includes('/sign-up'));

			// Should be redirected to home page or verification page
			const currentUrl = page.url();
			expect(currentUrl.includes('/') || currentUrl.includes('/verify')).toBeTruthy();
		} finally {
			// Clean up the test user
			await deleteTestUser(testUserData.email);
		}
	});

	test('shows validation errors for invalid sign-up data', async ({ page, goto }) => {
		test.slow();

		// Navigate to sign-up page
		await goto('/sign-up');
		await waitForPageLoad(page);

		// Try to submit with invalid data
		await page.getByPlaceholder('Name').fill('A'); // Too short
		await page.getByPlaceholder('Email address').fill('invalid-email'); // Invalid email
		await page.getByPlaceholder('Password').fill('weak'); // Weak password

		await page.getByRole('button', { name: 'Sign Up with Email' }).click();

		// Should show validation errors (form should not submit)
		await expect(page).toHaveURL('/sign-up');
	});

	test('shows error when trying to sign up with existing email', async ({ page, goto }) => {
		test.slow();

		// Navigate to sign-up page
		await goto('/sign-up');
		await waitForPageLoad(page);

		// Try to sign up with existing test user email
		await page.getByPlaceholder('Name').fill('Duplicate User');
		await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
		await page.getByPlaceholder('Password').fill('ValidPass123!');

		await page.getByRole('button', { name: 'Sign Up with Email' }).click();

		// Wait a moment for any async operations
		await page.waitForTimeout(2000);

		// Debug: Check what's on the page
		console.log('Page content after form submission:');
		console.log('URL:', page.url());
		console.log('Page title:', await page.title());

		// Check for any error elements
		const alertElements = await page.locator('[role="alert"]').count();
		console.log('Alert elements found:', alertElements);

		const errorText = await page.locator('text=/error|Error|already exists|duplicate/i').count();
		console.log('Error text elements found:', errorText);

		// Should show error message - wait for it to appear
		await expect(page.locator('[role="alert"]')).toBeVisible({ timeout: 10000 });

		// Should stay on sign-up page
		await expect(page).toHaveURL('/sign-up');
	});

	test('navigates to sign-in page from sign-up page', async ({ page, goto }) => {
		// Navigate to sign-up page
		await goto('/sign-up');
		await waitForPageLoad(page);

		// Click the "Already have an account?" link
		await page.getByText('Already have an account?').click();

		// Should navigate to sign-in page
		await expect(page).toHaveURL('/sign-in');
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
	});

	test('navigates to sign-up page from sign-in page', async ({ page, goto }) => {
		// Navigate to sign-in page
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Click the "Sign up for an account" link
		await page.getByText('Sign up for an account').click();

		// Should navigate to sign-up page
		await expect(page).toHaveURL('/sign-up');
		await expect(page.getByText('Sign up to Foundation')).toBeVisible();
	});

	test('successfully signs in existing user', async ({ page, goto }) => {
		test.slow();

		// Navigate to sign-in page
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Fill in sign-in form with test user
		await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
		await page.getByPlaceholder('Password').fill(SIGNIN_TEST_USER.password);

		// Submit form
		await page.getByRole('button', { name: 'Sign In' }).click();

		// Should navigate away from sign-in page
		await page.waitForURL((url) => !url.pathname.includes('/sign-in'));

		// Should be on home page and see user is logged in
		await expect(page).toHaveURL('/');
		await expect(page.getByText(`Hello ${SIGNIN_TEST_USER.name}`)).toBeVisible();
	});

	test('shows error for invalid sign-in credentials', async ({ page, goto }) => {
		// Navigate to sign-in page
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Try to sign in with invalid credentials
		await page.getByPlaceholder('Email address').fill('<EMAIL>');
		await page.getByPlaceholder('Password').fill('wrongpassword');

		await page.getByRole('button', { name: 'Sign In' }).click();

		// Should show error message and stay on sign-in page
		await expect(page.locator('[role="alert"]')).toBeVisible({ timeout: 10000 });
		await expect(page).toHaveURL('/sign-in');
	});
});
