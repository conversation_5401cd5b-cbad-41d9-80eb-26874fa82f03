/**
 * Test user management utilities
 *
 * This file provides utilities for creating and managing test users
 * in the database for e2e tests.
 */
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { eq } from 'drizzle-orm';
import { user, account } from '../../server/db/schema/auth.js';
import { loadTestEnv } from '../../server/utils/env-loader.js';
import { scrypt, randomBytes } from 'crypto';
import { promisify } from 'util';

// Load test environment
loadTestEnv();

// Create database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

// Promisify scrypt for async usage
const scryptAsync = promisify(scrypt);

export interface TestUser {
	id: string;
	name: string;
	email: string;
	password: string;
}

/**
 * Hash password using scrypt (same algorithm as Better Auth)
 * @param password Plain text password
 * @returns Hashed password in the format salt:hash
 */
async function hashPassword(password: string): Promise<string> {
	const salt = randomBytes(16).toString('hex');
	const hash = (await scryptAsync(password, salt, 64)) as Buffer;
	return `${salt}:${hash.toString('hex')}`;
}

/**
 * Creates a test user directly in the database with properly hashed password
 * @param userData User data to create
 * @returns The created user data
 */
export async function createTestUser(userData: Omit<TestUser, 'id'>): Promise<TestUser> {
	const userId = `test_user_${Date.now()}_${Math.random().toString(36).substring(7)}`;

	// Hash the password using scrypt (same as Better Auth)
	const hashedPassword = await hashPassword(userData.password);

	// Insert user
	await db.insert(user).values({
		id: userId,
		name: userData.name,
		email: userData.email,
		emailVerified: true, // Set to true for testing
		createdAt: new Date(),
		updatedAt: new Date(),
	});

	// Insert account with properly hashed password
	await db.insert(account).values({
		id: `account_${userId}`,
		accountId: userData.email,
		providerId: 'credential',
		userId: userId,
		password: hashedPassword, // Now properly hashed
		createdAt: new Date(),
		updatedAt: new Date(),
	});

	return {
		id: userId,
		...userData,
	};
}

/**
 * Deletes a test user from the database
 * @param email User email to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	// Find user by email
	const userRecord = await db.select().from(user).where(eq(user.email, email)).limit(1);

	if (userRecord.length > 0) {
		const userId = userRecord[0].id;

		// Delete account first (foreign key constraint)
		await db.delete(account).where(eq(account.userId, userId));

		// Delete user
		await db.delete(user).where(eq(user.id, userId));
	}
}

/**
 * Generates a unique test email
 * @param prefix Email prefix
 * @returns Unique test email
 */
export function generateTestEmail(prefix = 'test'): string {
	const timestamp = Date.now();
	const random = Math.random().toString(36).substring(7);
	return `${prefix}-${timestamp}-${random}@example.com`;
}

/**
 * Generates test user data
 * @param overrides Optional overrides for user data
 * @returns Test user data
 */
export function generateTestUserData(overrides: Partial<Omit<TestUser, 'id'>> = {}): Omit<TestUser, 'id'> {
	return {
		name: 'Test User',
		email: generateTestEmail(),
		password: 'TestPass123!',
		...overrides,
	};
}

/**
 * Pre-defined test user for sign-in tests
 * This user should be created once and reused for sign-in tests
 */
export const SIGNIN_TEST_USER = {
	name: 'Sign In Test User',
	email: '<EMAIL>',
	password: 'SignInTest123!',
};

/**
 * Ensures the sign-in test user exists in the database
 */
export async function ensureSignInTestUser(): Promise<void> {
	// Check if user already exists
	const existingUser = await db.select().from(user).where(eq(user.email, SIGNIN_TEST_USER.email)).limit(1);

	if (existingUser.length === 0) {
		try {
			await createTestUser(SIGNIN_TEST_USER);
		} catch (error) {
			// If user creation fails (e.g., user already exists), that's okay for our test user
			console.log('Test user creation failed, user may already exist:', error);
		}
	}
}

/**
 * Cleans up all test users (users with test emails)
 */
export async function cleanupTestUsers(): Promise<void> {
	// Delete test users (emails containing 'test-' or ending with '@example.com')
	const testUsers = await db.select().from(user).where(
		// This is a simple approach - in production you might want more sophisticated filtering
		eq(user.email, SIGNIN_TEST_USER.email)
	);

	for (const testUser of testUsers) {
		await deleteTestUser(testUser.email);
	}
}
