/**
 * Test user management utilities
 *
 * This file provides utilities for creating and managing test users
 * using Better Auth's API to ensure proper password hashing.
 */
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { eq } from 'drizzle-orm';
import { user, account } from '../../server/db/schema/auth.js';
import { loadTestEnv } from '../../server/utils/env-loader.js';
import { auth } from '../../server/utils/auth.js';

// Load test environment
loadTestEnv();

// Create database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

export interface TestUser {
	id: string;
	name: string;
	email: string;
	password: string;
}

/**
 * Creates a test user using Better Auth's signUpEmail function
 * This ensures the password is hashed correctly using Better Auth's internal methods
 * @param userData User data to create
 * @returns The created user data
 */
export async function createTestUser(userData: Omit<TestUser, 'id'>): Promise<TestUser> {
	try {
		// Use Better Auth's signUpEmail to create the user with proper password hashing
		const result = await auth.api.signUpEmail({
			body: {
				name: userData.name,
				email: userData.email,
				password: userData.password,
			},
		});

		if (!result.user) {
			throw new Error('Failed to create user via Better Auth');
		}

		return {
			id: result.user.id,
			name: result.user.name,
			email: result.user.email,
			password: userData.password, // Return the original password for test use
		};
	} catch (error) {
		// If user already exists, try to find them in the database
		if (error instanceof Error && error.message.includes('already exists')) {
			const existingUser = await db.select().from(user).where(eq(user.email, userData.email)).limit(1);

			if (existingUser.length > 0) {
				return {
					id: existingUser[0].id,
					name: existingUser[0].name,
					email: existingUser[0].email,
					password: userData.password,
				};
			}
		}

		throw error;
	}
}

/**
 * Deletes a test user from the database
 * @param email User email to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	// Find user by email
	const userRecord = await db.select().from(user).where(eq(user.email, email)).limit(1);

	if (userRecord.length > 0) {
		const userId = userRecord[0].id;

		// Delete account first (foreign key constraint)
		await db.delete(account).where(eq(account.userId, userId));

		// Delete user
		await db.delete(user).where(eq(user.id, userId));
	}
}

/**
 * Generates a unique test email
 * @param prefix Email prefix
 * @returns Unique test email
 */
export function generateTestEmail(prefix = 'test'): string {
	const timestamp = Date.now();
	const random = Math.random().toString(36).substring(7);
	return `${prefix}-${timestamp}-${random}@example.com`;
}

/**
 * Generates test user data
 * @param overrides Optional overrides for user data
 * @returns Test user data
 */
export function generateTestUserData(overrides: Partial<Omit<TestUser, 'id'>> = {}): Omit<TestUser, 'id'> {
	return {
		name: 'Test User',
		email: generateTestEmail(),
		password: 'TestPass123!',
		...overrides,
	};
}

/**
 * Pre-defined test user for sign-in tests
 * This user should be created once and reused for sign-in tests
 */
export const SIGNIN_TEST_USER = {
	name: 'Sign In Test User',
	email: '<EMAIL>',
	password: 'SignInTest123!',
};

/**
 * Ensures the sign-in test user exists in the database
 */
export async function ensureSignInTestUser(): Promise<void> {
	// Check if user already exists
	const existingUser = await db.select().from(user).where(eq(user.email, SIGNIN_TEST_USER.email)).limit(1);

	if (existingUser.length === 0) {
		try {
			await createTestUser(SIGNIN_TEST_USER);
		} catch (error) {
			// If user creation fails (e.g., user already exists), that's okay for our test user
			console.log('Test user creation failed, user may already exist:', error);
		}
	}
}

/**
 * Cleans up all test users (users with test emails)
 */
export async function cleanupTestUsers(): Promise<void> {
	// Delete test users (emails containing 'test-' or ending with '@example.com')
	const testUsers = await db.select().from(user).where(
		// This is a simple approach - in production you might want more sophisticated filtering
		eq(user.email, SIGNIN_TEST_USER.email)
	);

	for (const testUser of testUsers) {
		await deleteTestUser(testUser.email);
	}
}
